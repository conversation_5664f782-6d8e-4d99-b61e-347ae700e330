import { useState, useEffect, useMemo, useRef } from 'react';
import { useForm } from 'react-hook-form';
import {
  ProblemStatementResponse,
  CountryOption,
  IndustryOption,
} from '../../../types';
import Button from '@/components/ui/ButtonComponent';
import { PencilLine, X, ChevronDown } from 'lucide-react';
import FormSelectBox from '@/components/forms/FormSelectBox';
import { useGetCountries } from '@/hooks/apiQueryHooks/userQueryHooks';
import { useGetIndustryCategories } from '@/hooks/apiQueryHooks/eduQueryHooks';
import { Helper } from '@/utils/helpers';
import { GoalsHelper } from '@/features/projectManagementGoals/utils/helper';
import { countryFlags } from '@/utils/helpers/country-flags-data';
import { sdgGoalsCategories } from '../../../data/constants';

interface DescriptionTabContentProps {
  data: ProblemStatementResponse;
}

interface DescriptionFormData {
  title: string;
  countries: string[];
  description: string;
  descriptionVideoUrl: string;
  descriptionFile: File;
  focusCountries?: CountryOption[];
  industryCategoryRefs?: IndustryOption[];
}

export default function DescriptionTabContent({
  data,
}: DescriptionTabContentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(data.title);
  const [editedDescription, setEditedDescription] = useState(data.description);

  // Focus Countries state
  const [selectedCountries, setSelectedCountries] = useState<
    Array<{ code: string; name: string }>
  >(data.focusCountries || []);
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [countryError, setCountryError] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Goals Categories state
  const [selectedGoalsCategories, setSelectedGoalsCategories] = useState<
    Array<{ categoryRef: string; categoryName: string }>
  >([]);
  const [showGoalsCategoriesDropdown, setShowGoalsCategoriesDropdown] =
    useState(false);
  const goalsCategoriesDropdownRef = useRef<HTMLDivElement>(null);

  // Goals Subcategories state
  const [selectedGoalsSubcategories, setSelectedGoalsSubcategories] = useState<
    Array<{ subcategoryRef: string; subcategoryName: string }>
  >([]);
  const [showGoalsSubcategoriesDropdown, setShowGoalsSubcategoriesDropdown] =
    useState(false);
  const goalsSubcategoriesDropdownRef = useRef<HTMLDivElement>(null);

  const { data: countries } = useGetCountries();
  const countryOptions = Helper.createCountriesOptionsArray(
    countries?.data || { countries: [] },
  );

  const { data: industries, isLoading: isLoadingIndustries } =
    useGetIndustryCategories({
      staleTime: 1000 * 60 * 60 * 24,
      cacheTime: 1000 * 60 * 60 * 25,
    });

  // Create options arrays
  const industryOptions = GoalsHelper.createCategoryOptionsArr(
    industries?.data || [],
  );

  // Preselect values from API response data - memoized to prevent infinite re-renders
  const preselectedCountries = useMemo(
    () =>
      countryOptions.filter(option =>
        data.focusCountries.some(country => country.name === option.value),
      ),
    [countryOptions, data.focusCountries],
  );

  const preselectedIndustries = useMemo(
    () =>
      industryOptions.filter(option =>
        data.focusIndustries.some(industry => industry.id === option.value),
      ),
    [industryOptions, data.focusIndustries],
  );

  // Initialize Goals Categories from API response data
  const preselectedGoalsCategories = useMemo(() => {
    if (!data.goalsCategories || data.goalsCategories.length === 0) return [];

    return data.goalsCategories.map(category => {
      const categoryData = sdgGoalsCategories.find(
        sdgCategory => sdgCategory.categoryRef === category.id,
      );
      return {
        categoryRef: category.id,
        categoryName: categoryData?.categoryName || category.name,
      };
    });
  }, [data.goalsCategories]);

  // Initialize Goals Subcategories from API response data
  const preselectedGoalsSubcategories = useMemo(() => {
    if (!data.goalsSubcategories || data.goalsSubcategories.length === 0)
      return [];

    return data.goalsSubcategories.map(subcategory => {
      // Find the subcategory in the sdgGoalsCategories data
      let subcategoryData = null;
      for (const category of sdgGoalsCategories) {
        subcategoryData = category.subcategories.find(
          sub => sub.subcategoryRef === subcategory.id,
        );
        if (subcategoryData) break;
      }

      return {
        subcategoryRef: subcategory.id,
        subcategoryName: subcategoryData?.subcategoryName || subcategory.name,
      };
    });
  }, [data.goalsSubcategories]);

  const {
    control,
    formState: { errors },
    reset,
  } = useForm<DescriptionFormData>({
    defaultValues: {
      focusCountries: [],
      industryCategoryRefs: [],
    },
  });

  // Initialize Goals Categories and Subcategories state
  useEffect(() => {
    if (preselectedGoalsCategories.length > 0) {
      setSelectedGoalsCategories(preselectedGoalsCategories);
    }
    if (preselectedGoalsSubcategories.length > 0) {
      setSelectedGoalsSubcategories(preselectedGoalsSubcategories);
    }
  }, [preselectedGoalsCategories, preselectedGoalsSubcategories]);

  // Update form when preselected values are available
  useEffect(() => {
    if (preselectedCountries.length > 0 || preselectedIndustries.length > 0) {
      reset({
        focusCountries: preselectedCountries,
        industryCategoryRefs: preselectedIndustries,
      });
    }
  }, [preselectedCountries, preselectedIndustries, reset]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    // TODO: Implement API call to save changes
    console.log('Saving changes:', {
      title: editedTitle,
      description: editedDescription,
    });
    setIsEditing(false);
  };

  // Focus Countries helper functions
  const handleAddCountry = (countryName: string) => {
    setCountryError('');

    // Check if country is already selected
    if (selectedCountries.some(country => country.name === countryName)) {
      setCountryError('Country is already selected');
      return;
    }

    // Check maximum limit
    if (selectedCountries.length >= 5) {
      setCountryError('Maximum 5 countries can be selected');
      return;
    }

    // Find the country data from the countries data
    const countryData = countries?.data?.countries?.find(
      c => c.shortName === countryName,
    );
    if (countryData && countryData.shortName && countryData.countryCode) {
      setSelectedCountries(prev => [
        ...prev,
        { code: countryData.countryCode!, name: countryData.shortName! },
      ]);
    }
    setShowCountryDropdown(false);
  };

  const handleRemoveCountry = (countryName: string) => {
    setSelectedCountries(prev =>
      prev.filter(country => country.name !== countryName),
    );
    setCountryError('');
  };

  // Get available countries for dropdown (excluding already selected ones)
  const availableCountries = useMemo(() => {
    if (!countries?.data?.countries) return [];
    return countries.data.countries.filter(
      country =>
        country.shortName &&
        country.countryCode &&
        !selectedCountries.some(
          selected => selected.name === country.shortName,
        ),
    );
  }, [countries?.data?.countries, selectedCountries]);

  // Validate countries selection
  useEffect(() => {
    if (selectedCountries.length === 0) {
      setCountryError('At least one country must be selected');
    } else {
      setCountryError('');
    }
  }, [selectedCountries]);

  // Goals Categories helper functions
  const handleAddGoalsCategory = (categoryRef: string) => {
    // Check if category is already selected
    if (
      selectedGoalsCategories.some(
        category => category.categoryRef === categoryRef,
      )
    ) {
      return;
    }

    // Find the category data from sdgGoalsCategories
    const categoryData = sdgGoalsCategories.find(
      category => category.categoryRef === categoryRef,
    );

    if (categoryData) {
      setSelectedGoalsCategories(prev => [
        ...prev,
        {
          categoryRef: categoryData.categoryRef,
          categoryName: categoryData.categoryName,
        },
      ]);
    }
    setShowGoalsCategoriesDropdown(false);
  };

  const handleRemoveGoalsCategory = (categoryRef: string) => {
    setSelectedGoalsCategories(prev =>
      prev.filter(category => category.categoryRef !== categoryRef),
    );

    // Also remove any subcategories that belong to this category
    const categoryData = sdgGoalsCategories.find(
      cat => cat.categoryRef === categoryRef,
    );
    if (categoryData) {
      const subcategoryRefs = categoryData.subcategories.map(
        sub => sub.subcategoryRef,
      );
      setSelectedGoalsSubcategories(prev =>
        prev.filter(sub => !subcategoryRefs.includes(sub.subcategoryRef)),
      );
    }
  };

  // Get available categories for dropdown (excluding already selected ones)
  const availableGoalsCategories = useMemo(() => {
    return sdgGoalsCategories.filter(
      category =>
        !selectedGoalsCategories.some(
          selected => selected.categoryRef === category.categoryRef,
        ),
    );
  }, [selectedGoalsCategories]);

  // Goals Subcategories helper functions
  const handleAddGoalsSubcategory = (subcategoryRef: string) => {
    // Check if subcategory is already selected
    if (
      selectedGoalsSubcategories.some(
        sub => sub.subcategoryRef === subcategoryRef,
      )
    ) {
      return;
    }

    // Find the subcategory data from sdgGoalsCategories
    let subcategoryData = null;
    for (const category of sdgGoalsCategories) {
      subcategoryData = category.subcategories.find(
        sub => sub.subcategoryRef === subcategoryRef,
      );
      if (subcategoryData) break;
    }

    if (subcategoryData) {
      setSelectedGoalsSubcategories(prev => [
        ...prev,
        {
          subcategoryRef: subcategoryData.subcategoryRef,
          subcategoryName: subcategoryData.subcategoryName,
        },
      ]);
    }
    setShowGoalsSubcategoriesDropdown(false);
  };

  const handleRemoveGoalsSubcategory = (subcategoryRef: string) => {
    setSelectedGoalsSubcategories(prev =>
      prev.filter(sub => sub.subcategoryRef !== subcategoryRef),
    );
  };

  // Get available subcategories for dropdown (only from selected categories, excluding already selected ones)
  const availableGoalsSubcategories = useMemo(() => {
    const availableSubcategories = [];

    for (const selectedCategory of selectedGoalsCategories) {
      const categoryData = sdgGoalsCategories.find(
        cat => cat.categoryRef === selectedCategory.categoryRef,
      );

      if (categoryData) {
        const filteredSubcategories = categoryData.subcategories.filter(
          sub =>
            !selectedGoalsSubcategories.some(
              selected => selected.subcategoryRef === sub.subcategoryRef,
            ),
        );
        availableSubcategories.push(...filteredSubcategories);
      }
    }

    return availableSubcategories;
  }, [selectedGoalsCategories, selectedGoalsSubcategories]);

  // Click outside handler for dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowCountryDropdown(false);
      }

      if (
        goalsCategoriesDropdownRef.current &&
        !goalsCategoriesDropdownRef.current.contains(event.target as Node)
      ) {
        setShowGoalsCategoriesDropdown(false);
      }

      if (
        goalsSubcategoriesDropdownRef.current &&
        !goalsSubcategoriesDropdownRef.current.contains(event.target as Node)
      ) {
        setShowGoalsSubcategoriesDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="space-y-6">
      {/* Edit Button */}
      <div className="-mt-[72px] flex justify-end">
        {!isEditing ? (
          <Button
            onClick={handleEdit}
            className="flex items-center gap-2 bg-grayNine px-4 py-2 text-white hover:bg-primary"
          >
            Edit
            <PencilLine size={20} className="-mt-0.5" />
          </Button>
        ) : (
          <div className="">
            <Button
              onClick={handleSave}
              className="flex items-center gap-2 border border-primary bg-white px-4 py-2 text-primary hover:bg-primary hover:text-white"
            >
              Save
            </Button>
          </div>
        )}
      </div>

      {/* Title */}
      <div>
        {!isEditing ? (
          <p className="mb-4 rounded-lg border border-grayFifteen bg-white px-4 py-3 text-xl font-semibold text-gray-900">
            {data.title}
          </p>
        ) : (
          <input
            type="text"
            value={editedTitle}
            onChange={e => setEditedTitle(e.target.value)}
            className="mb-4 w-full rounded-md border border-gray-300 px-3 py-2 text-xl font-semibold text-gray-900 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Description */}
      <div>
        {!isEditing ? (
          <div className="prose max-w-none bg-white">
            <p className="whitespace-pre-wrap rounded-lg border border-grayFifteen px-4 py-3 text-gray-700">
              {data.description}
            </p>
          </div>
        ) : (
          <textarea
            value={editedDescription}
            onChange={e => setEditedDescription(e.target.value)}
            rows={6}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        )}
      </div>

      {/* Focus Countries */}
      <div className="flex items-center gap-2">
        <div className="flex items-center justify-between rounded-xl bg-peachOne p-2">
          <div className="mb-2 block h-fit whitespace-nowrap rounded bg-white p-2 text-sm font-medium text-gray-700">
            Focus Countries
          </div>
        </div>

        {/* Country Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <button
            type="button"
            onClick={() => {
              if (availableCountries.length > 0) {
                setShowCountryDropdown(!showCountryDropdown);
              }
            }}
            className={`flex w-fit items-center justify-between gap-3 rounded-xl border border-gray-300 ${
              selectedCountries.length >= 5
                ? 'cursor-not-allowed bg-gray-100'
                : 'bg-peachOne'
            } px-3 py-2 text-left text-sm`}
          >
            <div className="flex flex-wrap gap-2">
              {selectedCountries.map(country => (
                <div
                  key={country.code}
                  className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2.5 py-0.5"
                >
                  <span className="text-lg">
                    {countryFlags[country.name] || '🏳️'}
                  </span>
                  <span className="text-sm">{country.name}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveCountry(country.name)}
                    className="ml-1 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-black text-white hover:bg-red-600"
                  >
                    <X size={8} />
                  </button>
                </div>
              ))}
            </div>
            <ChevronDown size={20} className="text-gray-400" />
          </button>

          {showCountryDropdown && availableCountries.length > 0 && (
            <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg border border-gray-300 bg-white shadow-lg">
              {availableCountries.map(country => (
                <button
                  key={country.countryCode}
                  type="button"
                  onClick={() => handleAddCountry(country.shortName!)}
                  className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-50"
                >
                  <span className="text-lg">
                    {countryFlags[country.shortName!] || '🏳️'}
                  </span>
                  <span>{country.shortName}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
      {/* Error Message */}
      {countryError && <p className="text-sm text-red-600">{countryError}</p>}

      {/* Focus Industries */}
      <div>
        <FormSelectBox<DescriptionFormData>
          options={industryOptions}
          optionsArr={industryOptions}
          control={control}
          name="industryCategoryRefs"
          errors={errors}
          labelName="Focus Industries"
          placeholder="Select Industries"
          isClearable={false}
          isMulti
          isLoading={isLoadingIndustries}
          required={false}
        />
      </div>

      {/* Goals Categories */}
      <div className="flex items-center gap-2">
        <div className="flex items-center justify-between rounded-xl bg-peachOne p-2">
          <div className="mb-2 block h-fit whitespace-nowrap rounded bg-white p-2 text-sm font-medium text-gray-700">
            Goals Categories
          </div>
        </div>

        {/* Goals Categories Dropdown */}
        <div className="relative" ref={goalsCategoriesDropdownRef}>
          <button
            type="button"
            onClick={() => {
              if (availableGoalsCategories.length > 0) {
                setShowGoalsCategoriesDropdown(!showGoalsCategoriesDropdown);
              }
            }}
            className="flex w-fit items-center justify-between gap-3 rounded-xl border border-gray-300 bg-peachOne px-3 py-2 text-left text-sm"
          >
            <div className="flex flex-wrap gap-2">
              {selectedGoalsCategories.map(category => (
                <div
                  key={category.categoryRef}
                  className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2.5 py-0.5"
                >
                  <span className="text-sm">{category.categoryName}</span>
                  <button
                    type="button"
                    onClick={() =>
                      handleRemoveGoalsCategory(category.categoryRef)
                    }
                    className="ml-1 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-black text-white hover:bg-red-600"
                  >
                    <X size={8} />
                  </button>
                </div>
              ))}
            </div>
            <ChevronDown size={20} className="text-gray-400" />
          </button>

          {showGoalsCategoriesDropdown &&
            availableGoalsCategories.length > 0 && (
              <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg border border-gray-300 bg-white shadow-lg">
                {availableGoalsCategories.map(category => (
                  <button
                    key={category.categoryRef}
                    type="button"
                    onClick={() => handleAddGoalsCategory(category.categoryRef)}
                    className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-50"
                  >
                    <span>{category.categoryName}</span>
                  </button>
                ))}
              </div>
            )}
        </div>
      </div>

      {/* Goals Subcategories */}
      <div className="flex items-center gap-2">
        <div className="flex items-center justify-between rounded-xl bg-peachOne p-2">
          <div className="mb-2 block h-fit whitespace-nowrap rounded bg-white p-2 text-sm font-medium text-gray-700">
            Goals Subcategories
          </div>
        </div>

        {/* Goals Subcategories Dropdown */}
        <div className="relative" ref={goalsSubcategoriesDropdownRef}>
          <button
            type="button"
            onClick={() => {
              if (
                availableGoalsSubcategories.length > 0 &&
                selectedGoalsCategories.length > 0
              ) {
                setShowGoalsSubcategoriesDropdown(
                  !showGoalsSubcategoriesDropdown,
                );
              }
            }}
            className={`flex w-fit items-center justify-between gap-3 rounded-xl border border-gray-300 ${
              selectedGoalsCategories.length === 0
                ? 'cursor-not-allowed bg-gray-100'
                : 'bg-peachOne'
            } px-3 py-2 text-left text-sm`}
          >
            <div className="flex flex-wrap gap-2">
              {selectedGoalsSubcategories.map(subcategory => (
                <div
                  key={subcategory.subcategoryRef}
                  className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-2.5 py-0.5"
                >
                  <span className="text-sm">{subcategory.subcategoryName}</span>
                  <button
                    type="button"
                    onClick={() =>
                      handleRemoveGoalsSubcategory(subcategory.subcategoryRef)
                    }
                    className="ml-1 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-black text-white hover:bg-red-600"
                  >
                    <X size={8} />
                  </button>
                </div>
              ))}
            </div>
            <ChevronDown size={20} className="text-gray-400" />
          </button>

          {showGoalsSubcategoriesDropdown &&
            availableGoalsSubcategories.length > 0 &&
            selectedGoalsCategories.length > 0 && (
              <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg border border-gray-300 bg-white shadow-lg">
                {availableGoalsSubcategories.map(subcategory => (
                  <button
                    key={subcategory.subcategoryRef}
                    type="button"
                    onClick={() =>
                      handleAddGoalsSubcategory(subcategory.subcategoryRef)
                    }
                    className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-50"
                  >
                    <span>{subcategory.subcategoryName}</span>
                  </button>
                ))}
              </div>
            )}
        </div>
      </div>

      {/* Show message when no categories are selected */}
      {selectedGoalsCategories.length === 0 && (
        <p className="text-sm text-gray-500">
          Please select at least one Goals Category to enable subcategory
          selection.
        </p>
      )}
    </div>
  );
}
